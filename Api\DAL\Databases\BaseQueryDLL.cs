using Microsoft.EntityFrameworkCore;
using static DAL.Databases.EFHelper;

namespace DAL.Databases;

/// <summary>
/// 数据访问层基类
/// 提供基础的CRUD（增删改查）操作
/// </summary>
/// <typeparam name="TEntity">实体类型，必须是引用类型（class）</typeparam>
/// <typeparam name="TQueryable">查询模型类型，必须是引用类型（class）</typeparam>
public abstract class BaseQueryDLL<TEntity, TQueryable>(MyContext dbContext)
    where TEntity : class
    where TQueryable : PageQueryEntity
{
    protected readonly MyContext _dbContext = dbContext;

    /// <summary>
    /// 获取数据库上下文（为子类提供便捷访问）
    /// </summary>
    protected MyContext Context => _dbContext;

    /// <summary>
    /// 获取分页数据列表
    /// </summary>
    /// <param name="page">分页参数</param>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>分页结果</returns>
    public async Task<PageEntity<TEntity>> GetPageDataAsync(
        TQueryable queryable,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? defaultOrderBy = null)
    {
        var query = _dbContext.Set<TEntity>().AsQueryable();
        ArgumentNullException.ThrowIfNull(queryable);
        query = query.BuildQuery(queryable);
        // 如果查询结果没有排序，且提供了默认排序，则应用默认排序
        if (defaultOrderBy != null)
        {
            query = defaultOrderBy(query);
        }
        return await query.ToPageListAsync(new PageEntity<TEntity>() { PageIndex = queryable.PageIndex, PageSize = queryable.PageSize });
    }

    /// <summary>
    /// 获取所有数据列表
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>数据列表</returns>
    public async Task<List<TEntity>> GetListAsync(
        TQueryable queryable,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? defaultOrderBy = null)
    {
        var query = _dbContext.Set<TEntity>().AsQueryable();
        query = query.BuildQuery(queryable);
        // 如果查询结果没有排序，且提供了默认排序，则应用默认排序
        if (defaultOrderBy != null)
        {
            query = defaultOrderBy(query);
        }
        return await query.ToListAsync();
    }

    /// <summary>
    /// 获取所有数据列表
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>数据列表</returns>
    public IQueryable<TEntity> GetQueryable(TQueryable queryable)
    {
        var query = _dbContext.Set<TEntity>().AsQueryable();
        query = query.BuildQuery(queryable);
        return query;
    }

    /// <summary>
    /// 获取单条记录
    /// 根据查询条件返回第一条匹配的记录
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>返回单个实体，如果没有匹配的记录则返回null</returns>
    public async Task<TEntity?> GetFirstAsync(TQueryable queryable)
    {
        var query = _dbContext.Set<TEntity>().AsQueryable();
        query = query.BuildQuery(queryable);
        return await query.FirstOrDefaultAsync();
    }

    /// <summary>
    /// 获取单条记录，支持默认排序
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <param name="defaultOrderBy">默认排序表达式，如：q => q.OrderByDescending(x => x.CreateTime)</param>
    /// <returns>返回单个实体，如果没有匹配的记录则返回null</returns>
    public async Task<TEntity?> GetFirstAsync(
        TQueryable queryable,
        Func<IQueryable<TEntity>, IOrderedQueryable<TEntity>>? defaultOrderBy = null)
    {
        var query = _dbContext.Set<TEntity>().AsQueryable();
        query = query.BuildQuery(queryable);

        if (query is not IOrderedQueryable<TEntity> && defaultOrderBy != null)
        {
            query = defaultOrderBy(query);
        }

        return await query.FirstOrDefaultAsync();
    }

    /// <summary>
    /// 添加单条记录
    /// </summary>
    /// <param name="entity">要添加的实体对象</param>
    /// <returns>添加是否成功</returns>
    public async Task<bool> AddAsync(TEntity entity)
    {
        await _dbContext.Set<TEntity>().AddAsync(entity);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 批量添加记录
    /// </summary>
    /// <param name="entities">要添加的实体对象集合</param>
    /// <returns>添加是否成功</returns>
    public async Task<bool> AddRangeAsync(List<TEntity> entities)
    {
        await _dbContext.Set<TEntity>().AddRangeAsync(entities);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 更新单条记录
    /// </summary>
    /// <param name="entity">要更新的实体对象</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateAsync(TEntity entity)
    {
        _dbContext.Set<TEntity>().Update(entity);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 批量更新记录
    /// </summary>
    /// <param name="entities">要更新的实体对象集合</param>
    /// <returns>更新是否成功</returns>
    public async Task<bool> UpdateRangeAsync(IEnumerable<TEntity> entities)
    {
        _dbContext.Set<TEntity>().UpdateRange(entities);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 删除单条记录
    /// </summary>
    /// <param name="entity">要删除的实体对象</param>
    /// <returns>删除是否成功</returns>
    public async Task<bool> DeleteAsync(TEntity entity)
    {
        _dbContext.Set<TEntity>().Remove(entity);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 批量删除记录
    /// </summary>
    /// <param name="entities">要删除的实体对象集合</param>
    /// <returns>删除是否成功</returns>
    public async Task<bool> DeleteRangeAsync(IEnumerable<TEntity> entities)
    {
        _dbContext.Set<TEntity>().RemoveRange(entities);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 根据条件删除记录
    /// 先查询出符合条件的记录，然后批量删除
    /// </summary>
    /// <param name="queryable">查询条件模型</param>
    /// <returns>删除是否成功</returns>
    public async Task<bool> DeleteByConditionAsync(TQueryable queryable)
    {
        // 获取符合条件的记录
        var query = _dbContext.Set<TEntity>().AsQueryable();
        query = query.BuildQuery(queryable);
        var entities = await query.ToListAsync();
        if (entities.Count == 0) return false;

        // 批量删除
        _dbContext.Set<TEntity>().RemoveRange(entities);
        return await _dbContext.SaveChangesAsync() > 0;
    }

    /// <summary>
    /// 根据主键ID删除记录
    /// </summary>
    /// <param name="id">记录的主键ID</param>
    /// <returns>删除是否成功</returns>
    public async Task<bool> DeleteByIdAsync<TKey>(TKey id)
    {
        var entity = await _dbContext.Set<TEntity>().FindAsync(id);
        if (entity == null) return false;

        _dbContext.Set<TEntity>().Remove(entity);
        return await _dbContext.SaveChangesAsync() > 0;
    }
}
